namespace = KC_Event

### MASTER FIRE ###
event = {
	id = KC_Event.1
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		set_global_flag = KC_Setup_Fired

		#Energy Agronomics
		every_playable_country = {
			limit = { has_civic = KC_energy_agronomics }
			country_event = { id = KC_Event.2 }
		}

		#Galactic Food Industries
		every_playable_country = {
			limit = { has_civic = KC_galactic_food_industries }
			country_event = { id = KC_Event.3 }
		}
	}
}

### NORMAL EMPIRES ###

# Energy Agronomics

# Game Start
country_event = {
	id = KC_Event.2
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		give_technology = { tech = tech_food_processing_1 message = no }
		random_owned_planet = {
			limit = { is_capital = yes }

			if = {
				limit = {
					owner = {
						has_origin = origin_shattered_ring
					}
				}
				add_zone = {
					district = district_farming_uncapped
					zone = KC_zone_food_energy
					replace = yes
				}
			}

			else = {
				add_zone = {
					district = district_farming
					zone = KC_zone_food_energy
					replace = yes
				}
			}
		}
	}
}

### MEGACORP EMPIRES ###

# Galactic Food Industries

# Game Start
country_event = {
	id = KC_Event.3
	hide_window = yes
	is_triggered_only = yes

	immediate = {
		give_technology = { tech = tech_food_processing_1 message = no }
		random_owned_planet = {
			limit = { is_capital = yes }

			if = {
				limit = {
					owner = {
						has_origin = origin_shattered_ring
					}
				}
				add_zone = {
					district = district_farming_uncapped
					zone = KC_zone_food_trade
					replace = yes
				}
			}

			else = {
				add_zone = {
					district = district_farming
					zone = KC_zone_food_trade
					replace = yes
				}
			}
		}
	}
}

### Firewall Protocols ###

## KC_Event.4 - on-action path: apply immediately, roll bonus in after
country_event = {
    id = KC_Event.4
    hide_window = yes
    is_triggered_only = yes

    trigger = { has_valid_civic = KC_firewall_protocols }

    immediate = {
        from = { KC_Apply_Firewall_Protocols = yes }
    }

    after = {
        from = {
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }
    }
}

# KC_Event.5 - daily fixer: handle OWNED and POOL leaders
country_event = {
    id = KC_Event.5
    hide_window = yes

    trigger = {
        has_valid_civic = KC_firewall_protocols

        OR = {
            # owned leaders who need work
            any_owned_leader = {
                leader_class = official
                OR = {
                    NOT = { has_trait = leader_trait_righteous }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }

            # pool leaders who need work
            any_pool_leader = {
                leader_class = official
                OR = {
                    NOT = { has_trait = leader_trait_righteous }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
        }
    }

    mean_time_to_happen = { days = 1 }

    immediate = {
        # fix owned leaders
        every_owned_leader = {
            limit = {
                leader_class = official
                OR = {
                    NOT = { has_trait = leader_trait_righteous }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
            KC_Apply_Firewall_Protocols = yes
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }

        # fix pool leaders - guarantees pool cards are correct
        every_pool_leader = {
            limit = {
                leader_class = official
                OR = {
                    NOT = { has_trait = leader_trait_righteous }
                    has_trait = leader_trait_corrupt
                    has_trait = leader_trait_corrupt_2
                    kc_valid_for_additional_trait = yes
                }
            }
            KC_Apply_Firewall_Protocols = yes
            if = { limit = { kc_valid_for_additional_trait = yes } KC_Generate_Rando_Gov_trait = yes }
        }
    }
}