### FIREWALL PROTOCOLS ###

KC_Generate_Rando_Gov_trait = {
    random_list = {
        1 = { add_trait = { trait = leader_trait_adaptable show_message = no } }
        1 = { add_trait = { trait = leader_trait_resilient show_message = no } }
        1 = { add_trait = { trait = leader_trait_eager show_message = no } }
        1 = { add_trait = { trait = leader_trait_gifted show_message = no } }
        1 = { add_trait = { trait = leader_trait_bureaucrat show_message = no } }
        1 = { add_trait = { trait = leader_trait_environmental_engineer show_message = no } }
        1 = { add_trait = { trait = leader_trait_architectural_interest show_message = no } }
    }
}

KC_Apply_Firewall_Protocols = {
    # cleanse
    if = { limit = { has_trait = leader_trait_corrupt }   remove_trait = leader_trait_corrupt }
    if = { limit = { has_trait = leader_trait_corrupt_2 } remove_trait = leader_trait_corrupt_2 }

    # ensure righteous
    if = {
        limit = { NOT = { has_trait = leader_trait_righteous } }
        add_trait = { trait = leader_trait_righteous show_message = no }
    }
    # bonus roll handled by events after state settles
}
